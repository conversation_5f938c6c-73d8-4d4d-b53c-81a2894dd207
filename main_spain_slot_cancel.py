import requests
import time
import threading
from queue import Queue
from copy import deepcopy
from bs4 import BeautifulSoup

from spain_visa_login import user_login
from extension.logger import logger
from tool import get_new_proxy, get_random_user_agent, send_dd_msg, send_wx_msg

from config import headers, url_host
from user_manager import get_email_otp, save_user_2_redis_queue, get_users_with_queue_name, move_user_2_queue
from user_manager import spain_success_users, spain_error_users, spain_slot_cancel_users
from bypass_verify_code import bypass_verify_code_pipeline
from tool import update_booking_status, BOOK_STATUS

user_queue = Queue()


def user_account_cancel(user, session=requests.session()):
    try:
        url_page_delete = url_host + "/CHN/appointmentdata/BLSCancelAppointment"
        res_cancel_page = session.get(url_page_delete, verify=False, timeout=15)

        if res_cancel_page.status_code != 200:
            logger.error(f"#取消预约#请求错误 {res_cancel_page.url}: {res_cancel_page.status_code}: {res_cancel_page.text}")
            return False
        delete_soup = BeautifulSoup(res_cancel_page.text, "html.parser")
        cancel_forms = delete_soup.find("form")
        cancel_form_dict = {_.get("name"): _.get("value") for _ in cancel_forms.select("input")}
        cancel_form_dict.pop(None, None)
        cancel_form_dict["X-Requested-With"] = "XMLHttpRequest"
        cancel_form_dict["AppointmentNo"] = user.get("appointment_no", "")
        cancel_form_dict["Email"] = user.get("email")
        # 先删除旧的
        # delete_email_otp(user["email"])
        res_post_cancel = session.post(url_host + "/CHN/AppointmentData/ManageBLSCancelAppointment", data=cancel_form_dict, verify=False, timeout=15)
        if res_post_cancel.status_code != 200:
            logger.error(f"#取消预约#请求错误:{res_post_cancel.url}: {res_post_cancel.status_code}: {res_post_cancel.text}")
            return False
        if not res_post_cancel.json().get("success"):
            logger.error(f"#取消预约#请求错误:{res_post_cancel.url}: {res_post_cancel.status_code}: {res_post_cancel.json()}")
            return True

        emailverificationCode = res_post_cancel.json().get("emailverificationCode")
        appData = res_post_cancel.json().get("appData")

        time.sleep(2)  # 等待验证码发送
        email_opt = get_email_otp(user["email"])
        if not email_opt:
            logger.error("#取消预约# email_opt未取到")
            return False

        url_verify_email = url_host + f"/CHN/AppointmentData/VerifyEmailForCancelAppointment?code={email_opt}&codeData={emailverificationCode}"
        # 更新header参数
        session.headers.update({"requestverificationtoken": cancel_form_dict["__RequestVerificationToken"], "X-Requested-With": "XMLHttpRequest"})
        res_verify = session.post(url_verify_email, verify=False, timeout=15)
        if res_verify.status_code != 200 or not res_verify.json().get("success"):
            logger.error(f"#取消预约#请求错误:{res_verify.url}: {res_verify.status_code}: {res_verify.text}")
            return False

        url_cancel = url_host + f"/CHN/AppointmentData/CancelAppointment?data={appData}"
        res_cancel_app = session.get(url_cancel, verify=False, timeout=15)
        if res_cancel_app.status_code != 200:
            logger.error(f"#取消预约#请求错误:{res_cancel_app.url}: {res_cancel_app.status_code}: {res_cancel_app.text}")
            return False

        ## 从html解析验证码链接并且完成验证码校验
        res_verify, res_ = bypass_verify_code_pipeline(session, res_cancel_app.text)
        if not res_verify:
            logger.error(f"#取消预约#{user['email']}, 验证码校验失败")
            return False

        cancel_soup = BeautifulSoup(res_cancel_app.text, "html.parser")
        cancel_confirm_form = cancel_soup.find("form")
        cancel_info_form = {_.get("name"): _.get("value") for _ in cancel_confirm_form.select("input")}
        cancel_info_form["captchaId"] = res_verify["captchaId"]
        # cancel_info_form["AppointmentNo"] = user.get("appointment_no")
        cancel_info_form["CancelReason"] = "日期选错了"

        url_confirm_cancel = url_host + "/CHN/AppointmentData/CancelAppointment"
        res_confim = session.post(url_confirm_cancel, data=cancel_info_form, verify=False, timeout=15)
        if res_confim.status_code == 200 and res_confim.json().get("success"):
            msgs = f"#取消预约# {user['chnname']} | {user['email']} | {user['passportNO']} {user.get('centerCode')} 预约取消成功"
            logger.success(msgs)
            if user.get("queue_name") == spain_success_users:
                send_wx_msg(msgs)
            send_dd_msg(msgs)
            return True
        else:
            msgs = f"#取消预约# {user['chnname']}:{user['email']}-{user['passportNO']}:{user['centerCode']}, 预约取消失败:{res_confim.text}"
            logger.error(msgs)
            send_dd_msg(msgs)
            return False
    except Exception as e:
        logger.error(f"#取消预约#流程出错: {user['chnname']}:{user['email']}-{user['passportNO']}:{user['centerCode']}:{e.args[0]}")
        return False


# @timmer
def spain_date_cancel(user):
    if not user.get("appointment_no", ""):
        send_dd_msg(f"#取消预约失败#{user['chnname']} {user['email']} {user['centerCode']}, appointment_no不存在")
        time.sleep(10)
        return False

    header = deepcopy(headers)
    header["user-agent"] = get_random_user_agent()
    session = requests.session()
    session.headers = header

    # 登录状态失效 或者 cookie快要过期   --- 假设cookie过期时间是20分钟
    if not user.get("is_login", False) or int(time.time()) - int(user.get("updateTime", 0)) > 600:
        proxy = get_new_proxy()
        if not proxy:
            # logger.error("无可用代理")
            return None

        proxy_dict = {"http": proxy, "https": proxy}
        session.proxies.update(proxy_dict)

        logger.debug(f"##正在登录##{user.get('email')}: {user.get('passportNO')}, update:{user.get('updateTime','')}")
        flag_login, info_login = user_login(user, session)
        if not flag_login:
            user["is_login"] = False
            user["updateTime"] = int(time.time())
            logger.info(f"##预约取消## 登录失败 {user.get('email')}: {user.get('passportNO')}")
        else:
            logger.info(f"##预约取消## 登录成功 {user.get('email')}: {user.get('passportNO')}")
            cookie_dict = session.cookies.get_dict()
            user["cookies"] = cookie_dict
            user["proxy"] = proxy_dict["http"]
            user["is_login"] = True
            user["updateTime"] = int(time.time())

        # 失败成功都要更新到redis
        save_user_2_redis_queue(user)
    else:
        proxy = user.get("proxy")
        cookie = user.get("cookies")
        if proxy:
            proxy_dict = {"http": proxy, "https": proxy}
            session.proxies.update(proxy_dict)
        if cookie:
            session.cookies.update(cookie)

    canceled = user_account_cancel(user, session)
    logger.info(f"##预约取消## 取消{'成功' if canceled else '失败'}")
    user["is_login"] = canceled
    if canceled:
        user["status"] = "canceled"
        move_user_2_queue(user, spain_error_users)
        update_booking_status(user, BOOK_STATUS.APPOINTMENT_CANCELED, "预约已取消")
    else:
        pass
        # move_user_2_queue(user, spain_error_users)
    return canceled


def worker_keep_user_login():
    while not user_queue.empty():
        user = user_queue.get()
        spain_date_cancel(user)
        user_queue.task_done()


def brazil_users_cancel(thread_count=2):
    logger.info("#取消预约# 开始取消用户预约...")
    while True:
        # users_passportNO = ["*********", "*********"]
        all_users = get_users_with_queue_name(spain_slot_cancel_users)
        users = list(filter(lambda u: u.get("status") in ["ok"], all_users))
        if len(users) <= 0:
            time.sleep(30)
            continue

        for user in users:
            user["queue_name"] = spain_slot_cancel_users
            user_queue.put(user)

        threads = []
        for _ in range(thread_count):
            thread = threading.Thread(target=worker_keep_user_login, daemon=True)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()
        time.sleep(10)


if __name__ == "__main__":
    # 获取外部参数
    brazil_users_cancel()
